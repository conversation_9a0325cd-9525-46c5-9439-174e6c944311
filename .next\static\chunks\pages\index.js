/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/index"],{

/***/ "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Ckusan%5CDesktop%5CGenshin%20Dev%5CGCharacter%20Infomation%5Csrc%5Cpages%5Cindex.tsx&page=%2F!":
/*!****************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Ckusan%5CDesktop%5CGenshin%20Dev%5CGCharacter%20Infomation%5Csrc%5Cpages%5Cindex.tsx&page=%2F! ***!
  \****************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/\",\n      function () {\n        return __webpack_require__(/*! ./src/pages/index.tsx */ \"./src/pages/index.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWNsaWVudC1wYWdlcy1sb2FkZXIuanM/YWJzb2x1dGVQYWdlUGF0aD1DJTNBJTVDVXNlcnMlNUNrdXNhbiU1Q0Rlc2t0b3AlNUNHZW5zaGluJTIwRGV2JTVDR0NoYXJhY3RlciUyMEluZm9tYXRpb24lNUNzcmMlNUNwYWdlcyU1Q2luZGV4LnRzeCZwYWdlPSUyRiEiLCJtYXBwaW5ncyI6IjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsbUJBQU8sQ0FBQyxvREFBdUI7QUFDOUM7QUFDQTtBQUNBLE9BQU8sSUFBVTtBQUNqQixNQUFNLFVBQVU7QUFDaEI7QUFDQSxPQUFPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvPzRhMmYiXSwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgKHdpbmRvdy5fX05FWFRfUCA9IHdpbmRvdy5fX05FWFRfUCB8fCBbXSkucHVzaChbXG4gICAgICBcIi9cIixcbiAgICAgIGZ1bmN0aW9uICgpIHtcbiAgICAgICAgcmV0dXJuIHJlcXVpcmUoXCIuL3NyYy9wYWdlcy9pbmRleC50c3hcIik7XG4gICAgICB9XG4gICAgXSk7XG4gICAgaWYobW9kdWxlLmhvdCkge1xuICAgICAgbW9kdWxlLmhvdC5kaXNwb3NlKGZ1bmN0aW9uICgpIHtcbiAgICAgICAgd2luZG93Ll9fTkVYVF9QLnB1c2goW1wiL1wiXSlcbiAgICAgIH0pO1xuICAgIH1cbiAgIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Ckusan%5CDesktop%5CGenshin%20Dev%5CGCharacter%20Infomation%5Csrc%5Cpages%5Cindex.tsx&page=%2F!\n"));

/***/ }),

/***/ "./src/components/CharacterDisplay.tsx":
/*!*********************************************!*\
  !*** ./src/components/CharacterDisplay.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CharacterDisplay: function() { return /* binding */ CharacterDisplay; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _SkillDisplay__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./SkillDisplay */ \"./src/components/SkillDisplay.tsx\");\n/* harmony import */ var _utils_textFormatter__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/textFormatter */ \"./src/utils/textFormatter.tsx\");\n/* harmony import */ var _utils_statCalculator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/statCalculator */ \"./src/utils/statCalculator.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst CharacterDisplay = (param)=>{\n    let { character } = param;\n    _s();\n    const [level, setLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [ascension, setAscension] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [calculatedStats, setCalculatedStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        hp: character.stat.baseHp,\n        atk: character.stat.baseAtk,\n        def: character.stat.baseDef,\n        subStat: null\n    });\n    // Helper function to handle description which could be a string or an object\n    const getDescription = ()=>{\n        if (typeof character.desc === \"string\") {\n            return character.desc;\n        } else {\n            return Object.values(character.desc)[0] || \"\";\n        }\n    };\n    // Handle level changes and update ascension accordingly\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let newAscension = 0;\n        if (level >= 20) newAscension = 1;\n        if (level >= 40) newAscension = 2;\n        if (level >= 50) newAscension = 3;\n        if (level >= 60) newAscension = 4;\n        if (level >= 70) newAscension = 5;\n        if (level >= 80) newAscension = 6;\n        setAscension(newAscension);\n    }, [\n        level\n    ]);\n    // Calculate stats based on level and ascension\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const stats = (0,_utils_statCalculator__WEBPACK_IMPORTED_MODULE_4__.calculateStats)(character, level, ascension);\n        setCalculatedStats(stats);\n    }, [\n        character,\n        level,\n        ascension\n    ]);\n    // Format the special stat value for display\n    const formatSpecialStat = (name, value)=>{\n        if (name === \"FIGHT_PROP_ELEMENT_MASTERY\") {\n            return value.toFixed(0);\n        } else {\n            return \"\".concat((value * 100).toFixed(1), \"%\");\n        }\n    };\n    // Get special stat name for display\n    const getSpecialStatName = (type)=>{\n        const statMap = {\n            \"FIGHT_PROP_HP_PERCENT\": \"HP%\",\n            \"FIGHT_PROP_ATTACK_PERCENT\": \"ATK%\",\n            \"FIGHT_PROP_DEFENSE_PERCENT\": \"DEF%\",\n            \"FIGHT_PROP_PHYSICAL_ADD_HURT\": \"Physical DMG\",\n            \"FIGHT_PROP_CHARGE_EFFICIENCY\": \"Energy Recharge\",\n            \"FIGHT_PROP_ELEMENT_MASTERY\": \"Elemental Mastery\",\n            \"FIGHT_PROP_HEAL_ADD\": \"Healing Bonus\",\n            \"FIGHT_PROP_CRITICAL\": \"CRIT Rate\",\n            \"FIGHT_PROP_CRITICAL_HURT\": \"CRIT DMG\",\n            \"FIGHT_PROP_FIRE_ADD_HURT\": \"Pyro DMG\",\n            \"FIGHT_PROP_ELEC_ADD_HURT\": \"Electro DMG\",\n            \"FIGHT_PROP_WATER_ADD_HURT\": \"Hydro DMG\",\n            \"FIGHT_PROP_WIND_ADD_HURT\": \"Anemo DMG\",\n            \"FIGHT_PROP_ICE_ADD_HURT\": \"Cryo DMG\",\n            \"FIGHT_PROP_ROCK_ADD_HURT\": \"Geo DMG\",\n            \"FIGHT_PROP_GRASS_ADD_HURT\": \"Dendro DMG\"\n        };\n        return statMap[type] || type;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"character-container\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-100 p-4 rounded shadow mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-1/4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: \"/\".concat(character.icon, \".png\"),\n                                    alt: character.name,\n                                    className: \"w-32 h-32 rounded-full object-cover border-4 border-yellow-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-3/4 ml-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl font-bold\",\n                                            children: character.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-3 flex\",\n                                            children: Array(character.rarity).fill(0).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-yellow-400 text-xl\",\n                                                    children: \"★\"\n                                                }, i, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 19\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg italic\",\n                                            children: character.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-700 mt-2\",\n                                            children: (0,_utils_textFormatter__WEBPACK_IMPORTED_MODULE_3__.formatColoredText)(getDescription())\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 grid grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-bold\",\n                                                            children: \"Weapon Type:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                            lineNumber: 118,\n                                                            columnNumber: 20\n                                                        }, undefined),\n                                                        \" \",\n                                                        character.weapontype\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-bold\",\n                                                            children: \"Element:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                            lineNumber: 119,\n                                                            columnNumber: 20\n                                                        }, undefined),\n                                                        \" \",\n                                                        character.element\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-bold\",\n                                                            children: \"Constellation:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                            lineNumber: 120,\n                                                            columnNumber: 20\n                                                        }, undefined),\n                                                        \" \",\n                                                        character.constellationname\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-bold\",\n                                                            children: \"Region:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                            lineNumber: 123,\n                                                            columnNumber: 20\n                                                        }, undefined),\n                                                        \" \",\n                                                        character.region\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-bold\",\n                                                            children: \"Affiliation:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                            lineNumber: 124,\n                                                            columnNumber: 20\n                                                        }, undefined),\n                                                        \" \",\n                                                        character.affiliation\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-bold\",\n                                                            children: \"Birthday:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                            lineNumber: 125,\n                                                            columnNumber: 20\n                                                        }, undefined),\n                                                        \" \",\n                                                        character.birthday\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-2xl font-bold mb-4\",\n                        children: \"Character Stats\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    id: \"level-slider\",\n                                    type: \"range\",\n                                    min: 1,\n                                    max: 90,\n                                    step: 1,\n                                    value: level,\n                                    onChange: (e)=>setLevel(parseInt(e.target.value, 10)),\n                                    className: \"w-full h-2 bg-gray-300 rounded-lg appearance-none cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-[-30px] bg-primary-color text-white font-bold py-1 px-3 rounded-md pointer-events-none\",\n                                    style: {\n                                        left: \"calc(\".concat((level - 1) / 89 * 100, \"% - 15px)\"),\n                                        transform: \"translateX(-50%)\",\n                                        transition: \"left 0.1s ease-out\"\n                                    },\n                                    children: level\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-100 p-4 rounded shadow\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-3 text-lg font-bold\",\n                                children: [\n                                    \"Character Stats at Level \",\n                                    level,\n                                    \" (Ascension \",\n                                    ascension,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"w-full border-collapse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"bg-gray-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"border border-gray-400 p-2 text-left\",\n                                                    children: \"Stat\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"border border-gray-400 p-2 text-left\",\n                                                    children: \"Value\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"border border-gray-400 p-2\",\n                                                        children: \"HP\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"border border-gray-400 p-2\",\n                                                        children: calculatedStats.hp.toFixed(0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"border border-gray-400 p-2\",\n                                                        children: \"ATK\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"border border-gray-400 p-2\",\n                                                        children: calculatedStats.atk.toFixed(1)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"border border-gray-400 p-2\",\n                                                        children: \"DEF\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"border border-gray-400 p-2\",\n                                                        children: calculatedStats.def.toFixed(1)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            calculatedStats.subStat && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"border border-gray-400 p-2\",\n                                                        children: getSpecialStatName(calculatedStats.subStat.name)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"border border-gray-400 p-2\",\n                                                        children: formatSpecialStat(calculatedStats.subStat.name, calculatedStats.subStat.value)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-2xl font-bold mb-4\",\n                        children: \"Combat Talents\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SkillDisplay__WEBPACK_IMPORTED_MODULE_2__.SkillDisplay, {\n                                skill: character.skills.talent.normal,\n                                title: \"Normal Attack\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SkillDisplay__WEBPACK_IMPORTED_MODULE_2__.SkillDisplay, {\n                                skill: character.skills.talent.elemental,\n                                title: \"Elemental Skill\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SkillDisplay__WEBPACK_IMPORTED_MODULE_2__.SkillDisplay, {\n                                skill: character.skills.talent.burst,\n                                title: \"Elemental Burst\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                lineNumber: 199,\n                columnNumber: 7\n            }, undefined),\n            character.skills.passive && character.skills.passive.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-2xl font-bold mb-4\",\n                        children: \"Passive Talents\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: character.skills.passive.map((passive)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-100 p-4 rounded shadow\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"/\".concat(passive.icon, \".png\"),\n                                                alt: passive.name,\n                                                className: \"w-12 h-12 mr-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-lg font-bold\",\n                                                children: passive.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: (0,_utils_textFormatter__WEBPACK_IMPORTED_MODULE_3__.formatColoredText)(passive.desc.replace(/\\\\n/g, \" \"))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm mt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold\",\n                                                children: \"Unlocked at:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \" Ascension \",\n                                            passive.ascension\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, passive.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                lineNumber: 210,\n                columnNumber: 9\n            }, undefined),\n            character.skills.constellation && character.skills.constellation.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-2xl font-bold mb-4\",\n                        children: \"Constellations\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: character.skills.constellation.map((constellation, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-100 p-4 rounded shadow\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"/\".concat(constellation.icon, \".png\"),\n                                                alt: constellation.name,\n                                                className: \"w-12 h-12 mr-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-lg font-bold\",\n                                                children: [\n                                                    \"C\",\n                                                    index + 1,\n                                                    \": \",\n                                                    constellation.name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: (0,_utils_textFormatter__WEBPACK_IMPORTED_MODULE_3__.formatColoredText)(constellation.desc.replace(/\\\\n/g, \" \"))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, constellation.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                lineNumber: 237,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CharacterDisplay, \"VT6nxDYb1jKgbe/HrQk3QdL2fhA=\");\n_c = CharacterDisplay;\nvar _c;\n$RefreshReg$(_c, \"CharacterDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/CharacterDisplay.tsx\n"));

/***/ }),

/***/ "./src/components/SkillDisplay.tsx":
/*!*****************************************!*\
  !*** ./src/components/SkillDisplay.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SkillDisplay: function() { return /* binding */ SkillDisplay; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_textFormatter__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/textFormatter */ \"./src/utils/textFormatter.tsx\");\n\nvar _s = $RefreshSig$();\n\n\nconst SkillDisplay = (param)=>{\n    let { skill, title } = param;\n    _s();\n    const [level, setLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    // Find the current level upgrade data\n    const currentLevelData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return skill.upgrade.find((upgrade)=>upgrade.level === level) || skill.upgrade[0];\n    }, [\n        skill.upgrade,\n        level\n    ]);\n    // Parse the skill info to create the scaling table\n    const skillInfo = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return skill.info.filter((info)=>typeof info === \"string\").map((info)=>{\n            // Skip empty objects\n            if (!info || typeof info !== \"string\") return null;\n            // Split the info string by | to get label and value format\n            const [label, valueFormat] = info.split(\"|\");\n            if (!valueFormat) return {\n                label,\n                value: \"\"\n            };\n            // Parse the param references like {param1:F1P}\n            let value = valueFormat;\n            const paramRegex = /{param(\\d+)(:[\\w\\d]+)?}/g;\n            let match;\n            while((match = paramRegex.exec(valueFormat)) !== null){\n                const paramIndex = parseInt(match[1], 10) - 1;\n                const format = match[2] || \"\";\n                if (paramIndex >= 0 && paramIndex < currentLevelData.params.length) {\n                    let paramValue = currentLevelData.params[paramIndex];\n                    // Format the value based on the format specifier\n                    // F1P means format with 1 decimal place and as a percentage\n                    if (format === \":F1P\") {\n                        paramValue = (paramValue * 100).toFixed(1) + \"%\";\n                    } else if (format === \":F2P\") {\n                        paramValue = (paramValue * 100).toFixed(2) + \"%\";\n                    } else if (format === \":P\") {\n                        paramValue = (paramValue * 100).toFixed(0) + \"%\";\n                    } else if (format === \":F1\") {\n                        paramValue = paramValue.toFixed(1);\n                    } else {\n                        paramValue = paramValue.toString();\n                    }\n                    value = value.replace(match[0], paramValue);\n                }\n            }\n            return {\n                label,\n                value\n            };\n        }).filter(Boolean); // Remove null values\n    }, [\n        skill.info,\n        currentLevelData\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-100 p-4 rounded shadow\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"/\".concat(skill.icon, \".png\"),\n                            alt: skill.name,\n                            className: \"w-16 h-16 mr-3\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\SkillDisplay.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-xl font-bold\",\n                                children: [\n                                    title,\n                                    \": \",\n                                    skill.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\SkillDisplay.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\SkillDisplay.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\SkillDisplay.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\SkillDisplay.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 skill-description\",\n                children: typeof skill.desc === \"string\" && skill.desc.split(\"\\\\n\").map((line, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-1\",\n                        children: (0,_utils_textFormatter__WEBPACK_IMPORTED_MODULE_2__.formatColoredText)(line)\n                    }, i, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\SkillDisplay.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\SkillDisplay.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        htmlFor: \"level-slider-\".concat(skill.id),\n                        className: \"font-bold block mb-2\",\n                        children: [\n                            \"Talent Level: \",\n                            level\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\SkillDisplay.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        id: \"level-slider-\".concat(skill.id),\n                        type: \"range\",\n                        min: 1,\n                        max: 15,\n                        value: level,\n                        onChange: (e)=>setLevel(parseInt(e.target.value, 10)),\n                        className: \"w-full h-2 bg-gray-300 rounded-lg appearance-none cursor-pointer\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\SkillDisplay.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\SkillDisplay.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, undefined),\n            skillInfo.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                        className: \"font-bold mb-2\",\n                        children: \"Skill Attributes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\SkillDisplay.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: \"w-full border-collapse\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            children: skillInfo.map((info, index)=>info && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-400 p-2\",\n                                            children: info.label\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\SkillDisplay.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-400 p-2\",\n                                            children: info.value\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\SkillDisplay.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\SkillDisplay.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\SkillDisplay.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\SkillDisplay.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\SkillDisplay.tsx\",\n                lineNumber: 113,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\SkillDisplay.tsx\",\n        lineNumber: 75,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SkillDisplay, \"Sw2PTHCZXm+WTfg88tb9MszDJi4=\");\n_c = SkillDisplay;\nvar _c;\n$RefreshReg$(_c, \"SkillDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/SkillDisplay.tsx\n"));

/***/ }),

/***/ "./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_CharacterDisplay__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/CharacterDisplay */ \"./src/components/CharacterDisplay.tsx\");\n\nvar _s = $RefreshSig$();\n\n\nconst Home = ()=>{\n    _s();\n    const [characters, setCharacters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCharacterId, setSelectedCharacterId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchCharacters = async ()=>{\n            try {\n                setLoading(true);\n                // Fetch the list of all available character IDs\n                const response = await fetch(\"/api/characters\");\n                const { characterIds } = await response.json();\n                // Fetch data for each character\n                const characterPromises = characterIds.map(async (id)=>{\n                    const response = await fetch(\"/api/character/\".concat(id));\n                    return await response.json();\n                });\n                const loadedCharacters = await Promise.all(characterPromises);\n                setCharacters(loadedCharacters);\n                // Set the first character as selected by default\n                if (loadedCharacters.length > 0) {\n                    setSelectedCharacterId(loadedCharacters[0].id);\n                }\n                setLoading(false);\n            } catch (error) {\n                console.error(\"Failed to load character data\", error);\n                setLoading(false);\n            }\n        };\n        fetchCharacters();\n    }, []);\n    const selectedCharacter = characters.find((char)=>char.id === selectedCharacterId);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-2xl font-bold mb-4\",\n                children: \"Genshin Character Information\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, undefined),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: \"Loading character data...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"character-select\",\n                                className: \"mr-2 font-bold\",\n                                children: \"Select Character:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                id: \"character-select\",\n                                className: \"border p-2 rounded\",\n                                value: selectedCharacterId || \"\",\n                                onChange: (e)=>setSelectedCharacterId(Number(e.target.value)),\n                                children: characters.map((char)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: char.id,\n                                        children: [\n                                            char.name,\n                                            \" - \",\n                                            char.rarity,\n                                            \"★\"\n                                        ]\n                                    }, char.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 11\n                    }, undefined),\n                    selectedCharacter ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CharacterDisplay__WEBPACK_IMPORTED_MODULE_2__.CharacterDisplay, {\n                        character: selectedCharacter\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"No character selected.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\pages\\\\index.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Home, \"qB8g1XM1PGRkKsL4JaECsSuOqmA=\");\n_c = Home;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Home);\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/index.tsx\n"));

/***/ }),

/***/ "./src/utils/statCalculator.ts":
/*!*************************************!*\
  !*** ./src/utils/statCalculator.ts ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateStats: function() { return /* binding */ calculateStats; }\n/* harmony export */ });\n/**\r\n * Calculates character stats based on level and ascension\r\n */ function calculateStats(character, level, ascension) {\n    var _character_stat_upgrade_;\n    // Initial base stats\n    const initialStats = [\n        character.stat.baseHp,\n        character.stat.baseDef,\n        character.stat.baseAtk\n    ];\n    // Calculate level multiplier based on rarity\n    const levelMultiplierBase = 1 + (9.17431 - 1) / (100 - 1) * (level - 1);\n    let levelMultiplier = character.rarity === 4 ? Number(levelMultiplierBase.toFixed(3)) : Number((levelMultiplierBase + (-0.00168 + 0.0000748163 * (0.761486 * (5.11365 + level)) ** 2)).toFixed(3));\n    // Ascension multipliers\n    const ascensionMultiplier = [\n        0 / 182,\n        38 / 182,\n        65 / 182,\n        101 / 182,\n        128 / 182,\n        155 / 182,\n        182 / 182\n    ];\n    const maxAscensionValue = character.rarity === 4 ? [\n        3.577982,\n        3.577982,\n        3.577982\n    ] : [\n        4.106540,\n        4.1062406015,\n        4.10663546716\n    ];\n    // Calculate base stats with level and ascension\n    const ascensionValue = initialStats.map((value, index)=>value * maxAscensionValue[index] * ascensionMultiplier[ascension]);\n    const baseStats = initialStats.map((value, index)=>value * levelMultiplier + ascensionValue[index]);\n    // Determine sub stat from character's upgrade data\n    let subStat = null;\n    if (ascension >= 2 && ((_character_stat_upgrade_ = character.stat.upgrade[1]) === null || _character_stat_upgrade_ === void 0 ? void 0 : _character_stat_upgrade_.props)) {\n        const specialStatProps = character.stat.upgrade[1].props.find((prop)=>prop.type !== \"FIGHT_PROP_BASE_HP\" && prop.type !== \"FIGHT_PROP_BASE_DEFENSE\" && prop.type !== \"FIGHT_PROP_BASE_ATTACK\");\n        if (specialStatProps) {\n            const subStatValues = {\n                0: 0,\n                1: 0,\n                2: 1,\n                3: 2,\n                4: 2,\n                5: 3,\n                6: 4\n            };\n            // Find the base stat value at ascension 1\n            const baseValue = specialStatProps.value;\n            // Calculate the value at current ascension\n            const subStatMultiplier = subStatValues[ascension] || 0;\n            const currentValue = baseValue * subStatMultiplier;\n            if (baseValue && currentValue) {\n                subStat = {\n                    name: specialStatProps.type,\n                    value: currentValue\n                };\n            }\n        }\n    }\n    return {\n        hp: baseStats[0],\n        def: baseStats[1],\n        atk: baseStats[2],\n        subStat: subStat\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/statCalculator.ts\n"));

/***/ }),

/***/ "./src/utils/textFormatter.tsx":
/*!*************************************!*\
  !*** ./src/utils/textFormatter.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatColoredText: function() { return /* binding */ formatColoredText; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n/**\r\n * Formats text with color tags like <color=#FFD780FF>colored text</color>\r\n * Also handles LINK tags in the format {LINK#*********}link text{/LINK}\r\n */ const formatColoredText = (text)=>{\n    if (!text) return null;\n    // Split the text by color tags and link tags\n    const parts = text.split(/(<color=[^>]*>|<\\/color>|{LINK#[^}]*}|{\\/LINK})/g).filter(Boolean);\n    let currentColor = null;\n    let inLink = false;\n    let linkId = null;\n    return parts.map((part, index)=>{\n        // Check if this is a color opening tag\n        const colorMatch = part.match(/<color=([^>]*)>/);\n        if (colorMatch) {\n            currentColor = colorMatch[1];\n            return null;\n        }\n        // Check if this is a color closing tag\n        if (part === \"</color>\") {\n            currentColor = null;\n            return null;\n        }\n        // Check if this is a link opening tag\n        const linkMatch = part.match(/{LINK#([^}]*)}/);\n        if (linkMatch) {\n            inLink = true;\n            linkId = linkMatch[1];\n            return null;\n        }\n        // Check if this is a link closing tag\n        if (part === \"{/LINK}\") {\n            inLink = false;\n            linkId = null;\n            return null;\n        }\n        // If we're in a colored section, wrap the text in a span with the color\n        if (currentColor) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                style: {\n                    color: currentColor\n                },\n                children: part\n            }, index, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\utils\\\\textFormatter.tsx\",\n                lineNumber: 48,\n                columnNumber: 9\n            }, undefined);\n        }\n        // If we're in a link section, wrap the text in a span with a dotted underline\n        if (inLink) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-blue-500 border-b border-dotted border-blue-500 cursor-help\",\n                title: \"Reference: \".concat(linkId),\n                children: part\n            }, index, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\utils\\\\textFormatter.tsx\",\n                lineNumber: 57,\n                columnNumber: 9\n            }, undefined);\n        }\n        // Otherwise, just return the text\n        return part;\n    });\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///./src/utils/textFormatter.tsx\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Ckusan%5CDesktop%5CGenshin%20Dev%5CGCharacter%20Infomation%5Csrc%5Cpages%5Cindex.tsx&page=%2F!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);