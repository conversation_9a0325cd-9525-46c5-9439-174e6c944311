/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.tsx\");\n/* harmony import */ var _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./src\\pages\\index.tsx */ \"./src/pages/index.tsx\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/index\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/components/CharacterDisplay.tsx":
/*!*********************************************!*\
  !*** ./src/components/CharacterDisplay.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CharacterDisplay: () => (/* binding */ CharacterDisplay)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _SkillDisplay__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./SkillDisplay */ \"./src/components/SkillDisplay.tsx\");\n/* harmony import */ var _utils_textFormatter__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/textFormatter */ \"./src/utils/textFormatter.tsx\");\n/* harmony import */ var _utils_statCalculator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/statCalculator */ \"./src/utils/statCalculator.ts\");\n\n\n\n\n\nconst CharacterDisplay = ({ character })=>{\n    const [level, setLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [ascension, setAscension] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [calculatedStats, setCalculatedStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        hp: character.stat.baseHp,\n        atk: character.stat.baseAtk,\n        def: character.stat.baseDef,\n        subStat: null\n    });\n    // Helper function to handle description which could be a string or an object\n    const getDescription = ()=>{\n        if (typeof character.desc === \"string\") {\n            return character.desc;\n        } else {\n            return Object.values(character.desc)[0] || \"\";\n        }\n    };\n    // Handle level changes and update ascension accordingly\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let newAscension = 0;\n        if (level >= 20) newAscension = 1;\n        if (level >= 40) newAscension = 2;\n        if (level >= 50) newAscension = 3;\n        if (level >= 60) newAscension = 4;\n        if (level >= 70) newAscension = 5;\n        if (level >= 80) newAscension = 6;\n        setAscension(newAscension);\n    }, [\n        level\n    ]);\n    // Calculate stats based on level and ascension\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const stats = (0,_utils_statCalculator__WEBPACK_IMPORTED_MODULE_4__.calculateStats)(character, level, ascension);\n        setCalculatedStats(stats);\n    }, [\n        character,\n        level,\n        ascension\n    ]);\n    // Format the special stat value for display\n    const formatSpecialStat = (name, value)=>{\n        if (name === \"FIGHT_PROP_ELEMENT_MASTERY\") {\n            return value.toFixed(0);\n        } else {\n            return `${(value * 100).toFixed(1)}%`;\n        }\n    };\n    // Get special stat name for display\n    const getSpecialStatName = (type)=>{\n        const statMap = {\n            \"FIGHT_PROP_HP_PERCENT\": \"HP%\",\n            \"FIGHT_PROP_ATTACK_PERCENT\": \"ATK%\",\n            \"FIGHT_PROP_DEFENSE_PERCENT\": \"DEF%\",\n            \"FIGHT_PROP_PHYSICAL_ADD_HURT\": \"Physical DMG\",\n            \"FIGHT_PROP_CHARGE_EFFICIENCY\": \"Energy Recharge\",\n            \"FIGHT_PROP_ELEMENT_MASTERY\": \"Elemental Mastery\",\n            \"FIGHT_PROP_HEAL_ADD\": \"Healing Bonus\",\n            \"FIGHT_PROP_CRITICAL\": \"CRIT Rate\",\n            \"FIGHT_PROP_CRITICAL_HURT\": \"CRIT DMG\",\n            \"FIGHT_PROP_FIRE_ADD_HURT\": \"Pyro DMG\",\n            \"FIGHT_PROP_ELEC_ADD_HURT\": \"Electro DMG\",\n            \"FIGHT_PROP_WATER_ADD_HURT\": \"Hydro DMG\",\n            \"FIGHT_PROP_WIND_ADD_HURT\": \"Anemo DMG\",\n            \"FIGHT_PROP_ICE_ADD_HURT\": \"Cryo DMG\",\n            \"FIGHT_PROP_ROCK_ADD_HURT\": \"Geo DMG\",\n            \"FIGHT_PROP_GRASS_ADD_HURT\": \"Dendro DMG\"\n        };\n        return statMap[type] || type;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"character-container\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-100 p-4 rounded shadow mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-1/4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: `/${character.icon}.png`,\n                                    alt: character.name,\n                                    className: \"w-32 h-32 rounded-full object-cover border-4 border-yellow-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-3/4 ml-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl font-bold\",\n                                            children: character.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-3 flex\",\n                                            children: Array(character.rarity).fill(0).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-yellow-400 text-xl\",\n                                                    children: \"★\"\n                                                }, i, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 19\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg italic\",\n                                            children: character.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-700 mt-2\",\n                                            children: (0,_utils_textFormatter__WEBPACK_IMPORTED_MODULE_3__.formatColoredText)(getDescription())\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 grid grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-bold\",\n                                                            children: \"Weapon Type:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                            lineNumber: 118,\n                                                            columnNumber: 20\n                                                        }, undefined),\n                                                        \" \",\n                                                        character.weapontype\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-bold\",\n                                                            children: \"Element:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                            lineNumber: 119,\n                                                            columnNumber: 20\n                                                        }, undefined),\n                                                        \" \",\n                                                        character.element\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-bold\",\n                                                            children: \"Constellation:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                            lineNumber: 120,\n                                                            columnNumber: 20\n                                                        }, undefined),\n                                                        \" \",\n                                                        character.constellationname\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-bold\",\n                                                            children: \"Region:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                            lineNumber: 123,\n                                                            columnNumber: 20\n                                                        }, undefined),\n                                                        \" \",\n                                                        character.region\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-bold\",\n                                                            children: \"Affiliation:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                            lineNumber: 124,\n                                                            columnNumber: 20\n                                                        }, undefined),\n                                                        \" \",\n                                                        character.affiliation\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-bold\",\n                                                            children: \"Birthday:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                            lineNumber: 125,\n                                                            columnNumber: 20\n                                                        }, undefined),\n                                                        \" \",\n                                                        character.birthday\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-2xl font-bold mb-4\",\n                        children: \"Character Stats\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    id: \"level-slider\",\n                                    type: \"range\",\n                                    min: 1,\n                                    max: 90,\n                                    step: 1,\n                                    value: level,\n                                    onChange: (e)=>setLevel(parseInt(e.target.value, 10)),\n                                    className: \"w-full h-2 bg-gray-300 rounded-lg appearance-none cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-[-30px] bg-primary-color text-white font-bold py-1 px-3 rounded-md pointer-events-none\",\n                                    style: {\n                                        left: `calc(${(level - 1) / 89 * 100}% - 15px)`,\n                                        transform: \"translateX(-50%)\",\n                                        transition: \"left 0.1s ease-out\"\n                                    },\n                                    children: level\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-100 p-4 rounded shadow\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-3 text-lg font-bold\",\n                                children: [\n                                    \"Character Stats at Level \",\n                                    level,\n                                    \" (Ascension \",\n                                    ascension,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"w-full border-collapse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"bg-gray-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"border border-gray-400 p-2 text-left\",\n                                                    children: \"Stat\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"border border-gray-400 p-2 text-left\",\n                                                    children: \"Value\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"border border-gray-400 p-2\",\n                                                        children: \"HP\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"border border-gray-400 p-2\",\n                                                        children: calculatedStats.hp.toFixed(0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"border border-gray-400 p-2\",\n                                                        children: \"ATK\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"border border-gray-400 p-2\",\n                                                        children: calculatedStats.atk.toFixed(1)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"border border-gray-400 p-2\",\n                                                        children: \"DEF\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"border border-gray-400 p-2\",\n                                                        children: calculatedStats.def.toFixed(1)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            calculatedStats.subStat && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"border border-gray-400 p-2\",\n                                                        children: getSpecialStatName(calculatedStats.subStat.name)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"border border-gray-400 p-2\",\n                                                        children: formatSpecialStat(calculatedStats.subStat.name, calculatedStats.subStat.value)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-2xl font-bold mb-4\",\n                        children: \"Combat Talents\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SkillDisplay__WEBPACK_IMPORTED_MODULE_2__.SkillDisplay, {\n                                skill: character.skills.talent.normal,\n                                title: \"Normal Attack\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SkillDisplay__WEBPACK_IMPORTED_MODULE_2__.SkillDisplay, {\n                                skill: character.skills.talent.elemental,\n                                title: \"Elemental Skill\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SkillDisplay__WEBPACK_IMPORTED_MODULE_2__.SkillDisplay, {\n                                skill: character.skills.talent.burst,\n                                title: \"Elemental Burst\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                lineNumber: 199,\n                columnNumber: 7\n            }, undefined),\n            character.skills.passive && character.skills.passive.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-2xl font-bold mb-4\",\n                        children: \"Passive Talents\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: character.skills.passive.map((passive)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-100 p-4 rounded shadow\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: `/${passive.icon}.png`,\n                                                alt: passive.name,\n                                                className: \"w-12 h-12 mr-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-lg font-bold\",\n                                                children: passive.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: (0,_utils_textFormatter__WEBPACK_IMPORTED_MODULE_3__.formatColoredText)(passive.desc.replace(/\\\\n/g, \" \"))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm mt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold\",\n                                                children: \"Unlocked at:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \" Ascension \",\n                                            passive.ascension\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, passive.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                lineNumber: 210,\n                columnNumber: 9\n            }, undefined),\n            character.skills.constellation && character.skills.constellation.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-2xl font-bold mb-4\",\n                        children: \"Constellations\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: character.skills.constellation.map((constellation, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-100 p-4 rounded shadow\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: `/${constellation.icon}.png`,\n                                                alt: constellation.name,\n                                                className: \"w-12 h-12 mr-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-lg font-bold\",\n                                                children: [\n                                                    \"C\",\n                                                    index + 1,\n                                                    \": \",\n                                                    constellation.name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: (0,_utils_textFormatter__WEBPACK_IMPORTED_MODULE_3__.formatColoredText)(constellation.desc.replace(/\\\\n/g, \" \"))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, constellation.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n                lineNumber: 237,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\CharacterDisplay.tsx\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/CharacterDisplay.tsx\n");

/***/ }),

/***/ "./src/components/SkillDisplay.tsx":
/*!*****************************************!*\
  !*** ./src/components/SkillDisplay.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SkillDisplay: () => (/* binding */ SkillDisplay)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_textFormatter__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/textFormatter */ \"./src/utils/textFormatter.tsx\");\n\n\n\nconst SkillDisplay = ({ skill, title })=>{\n    const [level, setLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    // Find the current level upgrade data\n    const currentLevelData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return skill.upgrade.find((upgrade)=>upgrade.level === level) || skill.upgrade[0];\n    }, [\n        skill.upgrade,\n        level\n    ]);\n    // Parse the skill info to create the scaling table\n    const skillInfo = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return skill.info.filter((info)=>typeof info === \"string\").map((info)=>{\n            // Skip empty objects\n            if (!info || typeof info !== \"string\") return null;\n            // Split the info string by | to get label and value format\n            const [label, valueFormat] = info.split(\"|\");\n            if (!valueFormat) return {\n                label,\n                value: \"\"\n            };\n            // Parse the param references like {param1:F1P}\n            let value = valueFormat;\n            const paramRegex = /{param(\\d+)(:[\\w\\d]+)?}/g;\n            let match;\n            while((match = paramRegex.exec(valueFormat)) !== null){\n                const paramIndex = parseInt(match[1], 10) - 1;\n                const format = match[2] || \"\";\n                if (paramIndex >= 0 && paramIndex < currentLevelData.params.length) {\n                    let paramValue = currentLevelData.params[paramIndex];\n                    // Format the value based on the format specifier\n                    // F1P means format with 1 decimal place and as a percentage\n                    if (format === \":F1P\") {\n                        paramValue = (paramValue * 100).toFixed(1) + \"%\";\n                    } else if (format === \":F2P\") {\n                        paramValue = (paramValue * 100).toFixed(2) + \"%\";\n                    } else if (format === \":P\") {\n                        paramValue = (paramValue * 100).toFixed(0) + \"%\";\n                    } else if (format === \":F1\") {\n                        paramValue = paramValue.toFixed(1);\n                    } else {\n                        paramValue = paramValue.toString();\n                    }\n                    value = value.replace(match[0], paramValue);\n                }\n            }\n            return {\n                label,\n                value\n            };\n        }).filter(Boolean); // Remove null values\n    }, [\n        skill.info,\n        currentLevelData\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-100 p-4 rounded shadow\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: `/${skill.icon}.png`,\n                            alt: skill.name,\n                            className: \"w-16 h-16 mr-3\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\SkillDisplay.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-xl font-bold\",\n                                children: [\n                                    title,\n                                    \": \",\n                                    skill.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\SkillDisplay.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\SkillDisplay.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\SkillDisplay.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\SkillDisplay.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 skill-description\",\n                children: typeof skill.desc === \"string\" && skill.desc.split(\"\\\\n\").map((line, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-1\",\n                        children: (0,_utils_textFormatter__WEBPACK_IMPORTED_MODULE_2__.formatColoredText)(line)\n                    }, i, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\SkillDisplay.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\SkillDisplay.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        htmlFor: `level-slider-${skill.id}`,\n                        className: \"font-bold block mb-2\",\n                        children: [\n                            \"Talent Level: \",\n                            level\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\SkillDisplay.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        id: `level-slider-${skill.id}`,\n                        type: \"range\",\n                        min: 1,\n                        max: 15,\n                        value: level,\n                        onChange: (e)=>setLevel(parseInt(e.target.value, 10)),\n                        className: \"w-full h-2 bg-gray-300 rounded-lg appearance-none cursor-pointer\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\SkillDisplay.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\SkillDisplay.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, undefined),\n            skillInfo.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                        className: \"font-bold mb-2\",\n                        children: \"Skill Attributes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\SkillDisplay.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: \"w-full border-collapse\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            children: skillInfo.map((info, index)=>info && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-400 p-2\",\n                                            children: info.label\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\SkillDisplay.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-400 p-2\",\n                                            children: info.value\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\SkillDisplay.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\SkillDisplay.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\SkillDisplay.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\SkillDisplay.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\SkillDisplay.tsx\",\n                lineNumber: 113,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\components\\\\SkillDisplay.tsx\",\n        lineNumber: 75,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/SkillDisplay.tsx\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction MyApp({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 5,\n        columnNumber: 10\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyApp);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2FwcC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQytCO0FBRS9CLFNBQVNBLE1BQU0sRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQVk7SUFDL0MscUJBQU8sOERBQUNEO1FBQVcsR0FBR0MsU0FBUzs7Ozs7O0FBQ2pDO0FBRUEsaUVBQWVGLEtBQUtBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9nY2hhcmFjdGVyLWluZm9tYXRpb24vLi9zcmMvcGFnZXMvX2FwcC50c3g/ZjlkNiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IEFwcFByb3BzIH0gZnJvbSAnbmV4dC9hcHAnO1xyXG5pbXBvcnQgJy4uL3N0eWxlcy9nbG9iYWxzLmNzcyc7XHJcblxyXG5mdW5jdGlvbiBNeUFwcCh7IENvbXBvbmVudCwgcGFnZVByb3BzIH06IEFwcFByb3BzKSB7XHJcbiAgcmV0dXJuIDxDb21wb25lbnQgey4uLnBhZ2VQcm9wc30gLz47XHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IE15QXBwOyJdLCJuYW1lcyI6WyJNeUFwcCIsIkNvbXBvbmVudCIsInBhZ2VQcm9wcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_CharacterDisplay__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/CharacterDisplay */ \"./src/components/CharacterDisplay.tsx\");\n\n\n\nconst Home = ()=>{\n    const [characters, setCharacters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCharacterId, setSelectedCharacterId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchCharacters = async ()=>{\n            try {\n                setLoading(true);\n                // Fetch the list of all available character IDs\n                const response = await fetch(\"/api/characters\");\n                const { characterIds } = await response.json();\n                // Fetch data for each character\n                const characterPromises = characterIds.map(async (id)=>{\n                    const response = await fetch(`/api/character/${id}`);\n                    return await response.json();\n                });\n                const loadedCharacters = await Promise.all(characterPromises);\n                setCharacters(loadedCharacters);\n                // Set the first character as selected by default\n                if (loadedCharacters.length > 0) {\n                    setSelectedCharacterId(loadedCharacters[0].id);\n                }\n                setLoading(false);\n            } catch (error) {\n                console.error(\"Failed to load character data\", error);\n                setLoading(false);\n            }\n        };\n        fetchCharacters();\n    }, []);\n    const selectedCharacter = characters.find((char)=>char.id === selectedCharacterId);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-2xl font-bold mb-4\",\n                children: \"Genshin Character Information\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, undefined),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: \"Loading character data...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"character-select\",\n                                className: \"mr-2 font-bold\",\n                                children: \"Select Character:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                id: \"character-select\",\n                                className: \"border p-2 rounded\",\n                                value: selectedCharacterId || \"\",\n                                onChange: (e)=>setSelectedCharacterId(Number(e.target.value)),\n                                children: characters.map((char)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: char.id,\n                                        children: [\n                                            char.name,\n                                            \" - \",\n                                            char.rarity,\n                                            \"★\"\n                                        ]\n                                    }, char.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 11\n                    }, undefined),\n                    selectedCharacter ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CharacterDisplay__WEBPACK_IMPORTED_MODULE_2__.CharacterDisplay, {\n                        character: selectedCharacter\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"No character selected.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\pages\\\\index.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Home);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/index.tsx\n");

/***/ }),

/***/ "./src/utils/statCalculator.ts":
/*!*************************************!*\
  !*** ./src/utils/statCalculator.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateStats: () => (/* binding */ calculateStats)\n/* harmony export */ });\n/**\r\n * Calculates character stats based on level and ascension\r\n */ function calculateStats(character, level, ascension) {\n    // Initial base stats\n    const initialStats = [\n        character.stat.baseHp,\n        character.stat.baseDef,\n        character.stat.baseAtk\n    ];\n    // Calculate level multiplier based on rarity\n    const levelMultiplierBase = 1 + (9.17431 - 1) / (100 - 1) * (level - 1);\n    let levelMultiplier = character.rarity === 4 ? Number(levelMultiplierBase.toFixed(3)) : Number((levelMultiplierBase + (-0.00168 + 0.0000748163 * (0.761486 * (5.11365 + level)) ** 2)).toFixed(3));\n    // Ascension multipliers\n    const ascensionMultiplier = [\n        0 / 182,\n        38 / 182,\n        65 / 182,\n        101 / 182,\n        128 / 182,\n        155 / 182,\n        182 / 182\n    ];\n    const maxAscensionValue = character.rarity === 4 ? [\n        3.577982,\n        3.577982,\n        3.577982\n    ] : [\n        4.106540,\n        4.1062406015,\n        4.10663546716\n    ];\n    // Calculate base stats with level and ascension\n    const ascensionValue = initialStats.map((value, index)=>value * maxAscensionValue[index] * ascensionMultiplier[ascension]);\n    const baseStats = initialStats.map((value, index)=>value * levelMultiplier + ascensionValue[index]);\n    // Determine sub stat from character's upgrade data\n    let subStat = null;\n    if (ascension >= 2 && character.stat.upgrade[1]?.props) {\n        const specialStatProps = character.stat.upgrade[1].props.find((prop)=>prop.type !== \"FIGHT_PROP_BASE_HP\" && prop.type !== \"FIGHT_PROP_BASE_DEFENSE\" && prop.type !== \"FIGHT_PROP_BASE_ATTACK\");\n        if (specialStatProps) {\n            const subStatValues = {\n                0: 0,\n                1: 0,\n                2: 1,\n                3: 2,\n                4: 2,\n                5: 3,\n                6: 4\n            };\n            // Find the base stat value at ascension 1\n            const baseValue = specialStatProps.value;\n            // Calculate the value at current ascension\n            const subStatMultiplier = subStatValues[ascension] || 0;\n            const currentValue = baseValue * subStatMultiplier;\n            if (baseValue && currentValue) {\n                subStat = {\n                    name: specialStatProps.type,\n                    value: currentValue\n                };\n            }\n        }\n    }\n    return {\n        hp: baseStats[0],\n        def: baseStats[1],\n        atk: baseStats[2],\n        subStat: subStat\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/statCalculator.ts\n");

/***/ }),

/***/ "./src/utils/textFormatter.tsx":
/*!*************************************!*\
  !*** ./src/utils/textFormatter.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatColoredText: () => (/* binding */ formatColoredText)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n/**\r\n * Formats text with color tags like <color=#FFD780FF>colored text</color>\r\n * Also handles LINK tags in the format {LINK#*********}link text{/LINK}\r\n */ const formatColoredText = (text)=>{\n    if (!text) return null;\n    // Split the text by color tags and link tags\n    const parts = text.split(/(<color=[^>]*>|<\\/color>|{LINK#[^}]*}|{\\/LINK})/g).filter(Boolean);\n    let currentColor = null;\n    let inLink = false;\n    let linkId = null;\n    return parts.map((part, index)=>{\n        // Check if this is a color opening tag\n        const colorMatch = part.match(/<color=([^>]*)>/);\n        if (colorMatch) {\n            currentColor = colorMatch[1];\n            return null;\n        }\n        // Check if this is a color closing tag\n        if (part === \"</color>\") {\n            currentColor = null;\n            return null;\n        }\n        // Check if this is a link opening tag\n        const linkMatch = part.match(/{LINK#([^}]*)}/);\n        if (linkMatch) {\n            inLink = true;\n            linkId = linkMatch[1];\n            return null;\n        }\n        // Check if this is a link closing tag\n        if (part === \"{/LINK}\") {\n            inLink = false;\n            linkId = null;\n            return null;\n        }\n        // If we're in a colored section, wrap the text in a span with the color\n        if (currentColor) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                style: {\n                    color: currentColor\n                },\n                children: part\n            }, index, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\utils\\\\textFormatter.tsx\",\n                lineNumber: 48,\n                columnNumber: 9\n            }, undefined);\n        }\n        // If we're in a link section, wrap the text in a span with a dotted underline\n        if (inLink) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-blue-500 border-b border-dotted border-blue-500 cursor-help\",\n                title: `Reference: ${linkId}`,\n                children: part\n            }, index, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Genshin Dev\\\\GCharacter Infomation\\\\src\\\\utils\\\\textFormatter.tsx\",\n                lineNumber: 57,\n                columnNumber: 9\n            }, undefined);\n        }\n        // Otherwise, just return the text\n        return part;\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///./src/utils/textFormatter.tsx\n");

/***/ }),

/***/ "./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();