export interface CharacterData {
  id: number;
  name: string;
  rarity: number;
  title: string;
  desc: string;
  weapontype: string;
  element: string;
  constellationname: string;
  affiliation: string;
  region: string;
  birthday: string;
  cv: {
    cn: string;
    jp: string;
    en: string;
    ko: string;
  };
  type: string;
  icon: string;
  iconSide: string;
  iconImage: string;
  skills: {
    talent: {
      normal: SkillData;
      elemental: SkillData;
      burst: SkillData;
    };
    passive: PassiveSkill[];
    constellation: ConstellationSkill[];
  };
  stat: CharacterStat;
  available: boolean;
  material: number[];
  day: string[];
}

export interface SkillData {
  id: number;
  name: string;
  desc: string;
  icon: string;
  cd?: number;
  energy?: number;
  upgrade: SkillUpgrade[];
  info: (string | Record<string, never>)[];
}

export interface SkillUpgrade {
  level: number;
  params: number[];
  ascension: number;
  costs?: SkillUpgradeCost[];
}

export interface SkillUpgradeCost {
  id: number;
  count: number;
}

export interface PassiveSkill {
  id: number;
  name: string;
  desc: string;
  icon: string;
  ascension: number;
}

export interface ConstellationSkill {
  id: number;
  name: string;
  desc: string;
  icon: string;
}

export interface CharacterStat {
  baseAtk: number;
  baseDef: number;
  baseHp: number;
  curve: CurveInfo[];
  upgrade: StatUpgrade[];
}

export interface CurveInfo {
  type: string;
  curve: string;
}

export interface StatUpgrade {
  ascension: number;
  props: StatProperty[];
  costs?: SkillUpgradeCost[];
}

export interface StatProperty {
  type: string;
  value: number;
}